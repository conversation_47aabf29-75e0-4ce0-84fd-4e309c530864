application:
  <<: *default-app
  name: archive-post-worker

pod:
  namespace: archive
  domain: archive
  feature: post
  name: archive-post-worker
  type: worker
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/archive/post-worker/main.js
  version: "53066a4"
  image: bereal-monolith
  env: prod
  envRoot: prod
build-info:
  app: archive-post-worker
  version: "53066a4"
  branch: main
  commit: "53066a4"
  env: prod
  envRoot: prod
#  buildTime:

ingress:
  enable: true
  suffix:
strategy:
  rollingUpdate:
    maxSurge: "10%"
    maxUnavailable: "5%"

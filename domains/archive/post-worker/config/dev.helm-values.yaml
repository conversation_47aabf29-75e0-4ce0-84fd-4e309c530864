backend-apps:
  appVersion: "53066a4"
  datadog:
    env: dev
  name: archive-post-worker
  namespace: archive
  pod:
    command: domains/archive/post-worker/main.js
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "archive-post-w"
  resources:
    limits:
      cpu: "0"
      memory: "1500Mi"
    requests:
      cpu: "900m"
      memory: "1250Mi"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: platform/async
                operator: Exists
  tolerations:
    - effect: NoSchedule
      key: platform/async
      operator: Equal
      value: "true"
  keda:
    cpu:
      enable: true
      utilization: "70"
    enable: true
    max: 22
    metrics:
      enable: false
    metricsUrl: "http://tooling-keda-scaler-api.tooling.svc.cluster.local/api/deployments"
    min: 1
    pubsub:
      enable: true
      metadatas:
        - mode: "SubscriptionSize"
          subscriptionName: "content-post-written-archive-post-inject"
          value: "1"
    scaleDown:
      stabilizationWindowSeconds: 180
    scaleDownPercent: 25 # scale down max 25% at a time
    scaleDownPeriod: 180
    scaleUp:
      stabilizationWindowSeconds: 60

application:
  <<: *default-app
  name: archive-post-hard-deletion-worker

pod:
  namespace: archive
  domain: archive
  feature: post
  name: archive-post-hard-deletion-worker
  type: worker
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/archive/post-hard-deletion-worker/main.js
  version: "53066a4"
  image: bereal-monolith
  env: dev
  envRoot: dev
build-info:
  app: archive-post-hard-deletion-worker
  version: "53066a4"
  branch: main
  commit: "53066a4"
  env: dev
  envRoot: dev
#  buildTime:

ingress:
  enable: true
  suffix: main

backend-apps:
  appVersion: "53066a4"
  datadog:
    env: dev
  name: archive-post-hard-deletion-worker
  namespace: archive
  pod:
    command: domains/archive/post-hard-deletion-worker/main.js
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "archive-post-hard-deletion-w"
  resources:
    limits:
      cpu: "0"
      memory: "4000Mi"
    requests:
      cpu: 600m
      memory: "4000Mi"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: platform/async
                operator: Exists
  tolerations:
    - effect: NoSchedule
      key: platform/async
      operator: Equal
      value: "true"
  keda:
    cpu:
      enable: false
    enable: true
    max: 50
    metricsUrl: "http://tooling-keda-scaler-api.tooling.svc.cluster.local/api/deployments"
    min: 0

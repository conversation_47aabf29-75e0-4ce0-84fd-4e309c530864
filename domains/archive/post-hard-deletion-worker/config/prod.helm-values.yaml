backend-apps:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: platform/async
                operator: Exists
  appVersion: "53066a4"
  datadog:
    env: prod
  deployment:
    priorityClassName: low-priority
  keda:
    cpu:
      enable: false
    enable: true
    max: 50
    metricsUrl: "http://tooling-keda-scaler-api.bereal-prod.svc.cluster.local/api/deployments"
    min: 0
  metrics:
    enable: true
    metrics:
      - "cache_hits"
  name: archive-post-hard-deletion-worker
  namespace: archive
  pod:
    command: domains/archive/post-hard-deletion-worker/main.js
    domain: archive
    env: prod
    envRoot: prod
    feature: post
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
    type: worker
  podDisruptionBudget:
    enabled: false
  resources:
    limits:
      cpu: "0"
      memory: "4000Mi"
    requests:
      cpu: 600m
      memory: "4000Mi"
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "archive-post-hard-deletion-w"
  strategy:
    rollingUpdate:
      maxSurge: "10%"
      maxUnavailable: "5%"
  tolerations:
    - effect: NoSchedule
      key: platform/async
      operator: Equal
      value: "true"

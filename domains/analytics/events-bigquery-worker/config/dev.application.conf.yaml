application:
  <<: *default-app
  name: analytics-events-bigquery-worker

pod:
  namespace: analytics
  domain: analytics
  feature: events
  name: analytics-events-bigquery-worker
  type: worker
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/analytics/events-bigquery-worker/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: dev
  envRoot: dev
build-info:
  app: analytics-events-bigquery-worker
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: dev
  envRoot: dev
#  buildTime:

ingress:
  enable: true
  suffix: main

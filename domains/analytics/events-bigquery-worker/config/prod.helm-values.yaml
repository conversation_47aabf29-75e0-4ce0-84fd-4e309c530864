backend-apps:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: platform/async
                operator: Exists
  appVersion: "8af49e1"
  datadog:
    env: prod
  deployment:
    priorityClassName: low-priority
  keda:
    cpu:
      enable: true
      utilization: "70"
    enable: true
    max: 5
    metrics:
      enable: false
    metricsUrl: "http://tooling-keda-scaler-api.bereal-prod.svc.cluster.local/api/deployments"
    min: 1
    pubsub:
      enable: true
      metadatas:
        - mode: "SubscriptionSize"
          subscriptionName: "analytics-events-received-analytics-events-bigquery-writer-v1"
          value: "1000"
  metrics:
    enable: true
    metrics:
      - "cache_hits"
  name: analytics-events-bigquery-worker
  namespace: analytics
  pod:
    command: domains/analytics/events-bigquery-worker/main.js
    domain: analytics
    env: prod
    envRoot: prod
    feature: events
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
    type: worker
  resources:
    limits:
      cpu: "1000m"
      memory: "3000Mi"
    requests:
      cpu: "600m"
      memory: "1500Mi"
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "analytics-events-bigquery"
  strategy:
    rollingUpdate:
      maxSurge: "10%"
      maxUnavailable: "5%"
  tolerations:
    - effect: NoSchedule
      key: platform/async
      operator: Equal
      value: "true"

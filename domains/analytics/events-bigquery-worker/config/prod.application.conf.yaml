application:
  <<: *default-app
  name: analytics-events-bigquery-worker

pod:
  namespace: analytics
  domain: analytics
  feature: events
  name: analytics-events-bigquery-worker
  type: worker
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/analytics/events-bigquery-worker/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: prod
  envRoot: prod
build-info:
  app: analytics-events-bigquery-worker
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: prod
  envRoot: prod
#  buildTime:

ingress:
  enable: true
  suffix:
strategy:
  rollingUpdate:
    maxSurge: "10%"
    maxUnavailable: "5%"

backend-apps:
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: platform/async
                operator: Exists
  appVersion: "8af49e1"
  datadog:
    env: dev
  name: analytics-events-bigquery-worker
  namespace: analytics
  pod:
    command: domains/analytics/events-bigquery-worker/main.js
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "analytics-events-bigquery"
  resources:
    limits:
      cpu: "1000m"
      memory: "3000Mi"
    requests:
      cpu: "600m"
      memory: "1500Mi"
  tolerations:
    - effect: NoSchedule
      key: platform/async
      operator: Equal
      value: "true"
  keda:
    cpu:
      enable: true
      utilization: "70"
    enable: true
    max: 5
    metrics:
      enable: false
    metricsUrl: "http://tooling-keda-scaler-api.tooling.svc.cluster.local/api/deployments"
    min: 1
    pubsub:
      enable: true
      metadatas:
        - mode: "SubscriptionSize"
          subscriptionName: "analytics-events-received-analytics-events-bigquery-writer-v1"
          value: "1000"

backend-apps:
  appVersion: "8af49e1"
  datadog:
    env: prod
  keda:
    enable: true
    max: "3000"
    metricsUrl: "http://tooling-keda-scaler-api.bereal-prod.svc.cluster.local/api/deployments"
    min: "3"
  metrics:
    enable: true
    metrics:
      - "cache_hits"
  name: analytics-events-mobile-api
  namespace: analytics
  pod:
    command: domains/analytics/events-mobile-api/main.js
    domain: analytics
    env: prod
    envRoot: prod
    feature: events
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
    type: api
  resources:
    limits:
      cpu: "0"
      memory: "4000Mi"
    requests:
      cpu: "700m"
      memory: "3744Mi"
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "analytics-events-mobile-a"
  strategy:
    rollingUpdate:
      maxSurge: "10%"
      maxUnavailable: "5%"
  neg:
    enabled: true
  nginxSidecar:
    enabled: true
    port: 80 # the neg targets nginx sidecar container which listens on port 80
    resources:
      requests:
        cpu: "50m"
        memory: "256Mi"
      limits:
        memory: "1024Mi"

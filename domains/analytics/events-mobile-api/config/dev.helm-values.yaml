backend-apps:
  appVersion: "8af49e1"
  datadog:
    env: dev
  name: analytics-events-mobile-api
  namespace: analytics
  pod:
    command: domains/analytics/events-mobile-api/main.js
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "analytics-events-mobile-a"
  resources:
    limits:
      cpu: "0"
      memory: "4000Mi"
    requests:
      cpu: "700m"
      memory: "3744Mi"
  neg:
    enabled: true
  nginxSidecar:
    enabled: true
    port: 80 # the neg targets nginx sidecar container which listens on port 80
    resources:
      requests:
        cpu: "20m"
        memory: "50Mi"
      limits:
        memory: "512Mi"
  keda:
    enable: true
    max: "3000"
    metricsUrl: "http://tooling-keda-scaler-api.tooling.svc.cluster.local/api/deployments"
    min: "1"

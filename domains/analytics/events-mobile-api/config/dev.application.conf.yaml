application:
  <<: *default-app
  name: analytics-events-mobile-api
  serviceContext: /main/
  serviceUri: https://dev.mobile.bereal.team/main/

analytics:
  events:
    writeIntoDBForUsersIds:
      - '4L8MOp5vVATuKm85ZXkvx1kz9sm1' # fjuif userId
      - 'anXUaqrXL2TlqGeS1OgnY' # fjuif uid
pod:
  namespace: analytics
  domain: analytics
  feature: events
  name: analytics-events-mobile-api
  type: api
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/analytics/events-mobile-api/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: dev
  envRoot: dev
build-info:
  app: analytics-events-mobile-api
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: dev
  envRoot: dev
#  buildTime:

ingress:
  enable: true
  suffix: main

application:
  <<: *default-app
  name: analytics-events-mobile-api
  serviceContext: /
  serviceUri: https://mobile.bereal.com/
  maxConnections: 700

analytics:
  events:
    enablePublish: true
    writeIntoDBForUsersIds:
      - '0IAr8LwdKHcZLBJIcg75LUGn4383'
      - '19pCCqaYkATzdDxFey0P05f2im42'
      - '1sVJgZ2hcGc82l1zlWyLGI1l9bQ2'
      - '1sWmVskLgdSGqhnVYDbeqhmu6p63'
      - '2qabZQB_52UxX2ck6t0fX'
      - '3W8Ykfsg2YMm5JPOxtheeq8ud783'
      - '52OtY0rmpLgHCi7KdZmys5SmtKr1'
      - '5HAzjYyu3WZ78sMyTwiPIO2WYb22'
      - '7RClx3lXWTYjLlSku3RRxLly7Nm1'
      - '7UHf5XJMM1eKWgnhIRfelUyeb162'
      - '9psFkDEA1FYcerK7YC1lj0BQ7Wu1'
      - 'b6U0CKWKInZUkKzTgzxdGFnb4QS2'
      - 'cC4OF2UgkShJESq67HQw8I6ABt82'
      - 'cwGr3Y2KmAPVmDy7lk6Vq2lYlrl2'
      - 'e8KG7cCbVrNLGZxdkVQzrKIDmUu2'
      - 'Fpb1cB4IyjS3lRreHJ1YU7w49yG3'
      - 'H0rbY45SS5TdJrN04lXTUOdv2Lx2'
      - 'hBjYwu3f1lVXfVbjRNb5tFFSST02'
      - 'hRT2lWyqJONHGEkaSfZcn7HY7bG2'
      - 'hRyrhHgLEeXGznTxFeXlXdFLTmr1'
      - 'ic2zUECjXCP1dpzE6X1aPv1zi0I3'
      - 'IK3ahpLD8cfJD54Q7EV2GjPSj7O2'
      - 'IXvPt4IGdoVy06RIDnnuqQHAAKg1'
      - 'IzvA6aFvCfTQvdmRoSqGQ7wjfCH3'
      - 'LAegCydvWPY2G3y6w1UA0MZiZHW2'
      - 'm3LqlypmJCaDBdFUIeOX7in5Wmy1'
      - 'MtJqXA0IJGhGbLYaZYCO4AYonOj1'
      - 'N5d3xvABNNbJGxySIQ9sUBL9zyf1'
      - 'O1mvoRCpJPNu5uTh9tNekR99DrB2'
      - 'oghhtkAzsUUVFFB0oR65VuNzqqR2'
      - 'pYqZh9MG9-hSRFgM9jFyR'
      - 'QH3vFRfjNZjO3khgRSDH0'
      - 'r4UMkuWWQng5XJV5Ve325BD6Rjp1'
      - 'RmatsnXaHKes530xyixBJEMy8ds2'
      - 's16Y3gMxm1eVOUnL4VInAQKSMtY2'
      - 'SAtSssZut6hQteVVfIwDvZCOddA2'
      - 't9MfeAEuxqOKXnLBU2vFIJGWm1H3'
      - 'to1kfsz3xmSWVk14L56GBalwAzq2'
      - 'UsLaPD4BW7Np6Ds4EFkSx'
      - 'vIjg0sFcDoPap3J2FX9BYAaXLWv1'
      - 'WfnFfIUAIEYofnrcs2jz5p168zq1'
      - 'wnOE3ycNjIZKixEO5c9hSZKfX0l1'
      - 'yBZQYj6NndSh0gqX2pkOen4ty9L2'
      - 'ypPGWKAf5FVkbxkUhZo6W3KwjSm1'
      - 'ZJtdS4AhRFQrgtDCNw1tgBITaMg1'

pod:
  namespace: analytics
  domain: analytics
  feature: events
  name: analytics-events-mobile-api
  type: api
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/analytics/events-mobile-api/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: prod
  envRoot: prod
build-info:
  app: analytics-events-mobile-api
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: prod
  envRoot: prod
#  buildTime:

ingress:
  enable: true
  suffix:
strategy:
  rollingUpdate:
    maxSurge: "10%"
    maxUnavailable: "5%"

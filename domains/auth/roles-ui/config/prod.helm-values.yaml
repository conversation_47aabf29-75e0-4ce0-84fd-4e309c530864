backend-apps:
  appVersion: "8af49e1"
  datadog:
    env: prod
  keda:
    max: "3000"
    min: "3"
  metrics:
    enable: true
    metrics:
      - "cache_hits"
  name: auth-roles-ui
  namespace: auth
  pod:
    command: domains/auth/roles-ui/main.js
    domain: auth
    env: prod
    envRoot: prod
    feature: roles
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
    type: ui
  resources:
    limits:
      cpu: "0"
      memory: "4000Mi"
    requests:
      cpu: "1150m"
      memory: "3744Mi"
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: "true"
    name: "auth-roles-ui"
  strategy:
    rollingUpdate:
      maxSurge: "10%"
      maxUnavailable: "5%"
  neg:
    enabled: true
  nginxSidecar:
    enabled: true
    port: 80 # the neg targets nginx sidecar container which listens on port 80
    resources:
      requests:
        cpu: "50m"
        memory: "256Mi"
      limits:
        memory: "1024Mi"

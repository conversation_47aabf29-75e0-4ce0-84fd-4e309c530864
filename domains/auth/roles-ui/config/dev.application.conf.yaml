application:
  <<: *default-app
  name: auth-roles-ui
  oauth: true
  session: true
  serviceContext: /main/roles/
  serviceUri: https://tools.dev.bereal.team/main/roles/

auth:
  roles-ui:
    api:
      slack:
        slackEnable: false

hbs:
  fallbackIndex: index
  assets: ./assets
  views: ./

session:
  redis: *redis-session

oauth-client:
  <<: *default-oauth-client
  clientId: auth-roles-ui
  clientSecret: 791E5D52-20DB-4680-8139-284B54D71926
  roles:
    - 'staff'

pod:
  namespace: auth
  domain: auth
  feature: roles
  name: auth-roles-ui
  type: ui
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/auth/roles-ui/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: dev
  envRoot: dev
build-info:
  app: auth-roles-ui
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: dev
  envRoot: dev
#  buildTime:

ingress:
  enable: true
  suffix: main

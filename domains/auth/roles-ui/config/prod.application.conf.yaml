application:
  <<: *default-app
  name: auth-roles-ui
  oauth: true
  session: true
  serviceContext: /roles/
  serviceUri: https://tools.bereal.team/roles/


auth:
  roles-ui:
    api:
      lockRoles:
        - auth:super-admin
        - profile:realpeople
        - people
      superAdminRole: auth:super-admin
      slack:
        channel: T01570W3U9X/B05EBFAEBRN/d0G4DyKJAn3vbCsMU4hvxTg5
      slackEnable: true

hbs:
  fallbackIndex: index
  assets: ./assets
  views: ./

session:
  redis: *redis-session

oauth-client:
  <<: *default-oauth-client
  clientId: auth-roles-ui
  clientSecret: 5A0A8F77-4099-45E7-9C11-94416B4662BB
  roles:
    - 'staff'

pod:
  namespace: auth
  domain: auth
  feature: roles
  name: auth-roles-ui
  type: ui
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/auth/roles-ui/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: prod
  envRoot: prod
build-info:
  app: auth-roles-ui
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: prod
  envRoot: prod
#  buildTime:

ingress:
  enable: true
  suffix:
strategy:
  rollingUpdate:
    maxSurge: "10%"
    maxUnavailable: "5%"

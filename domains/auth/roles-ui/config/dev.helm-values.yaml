backend-apps:
  appVersion: "8af49e1"
  datadog:
    env: dev
  name: auth-roles-ui
  namespace: auth
  pod:
    command: domains/auth/roles-ui/main.js
    image: bereal-monolith
    registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  serviceAccount:
    annotations:
      iam.gke.io/gcp-service-account: "<EMAIL>"
    create: true
    name: "auth-roles-ui"
  resources:
    limits:
      cpu: "0"
      memory: "4000Mi"
    requests:
      cpu: "1150m"
      memory: "3744Mi"
  neg:
    enabled: true
  nginxSidecar:
    enabled: true
    port: 80 # the neg targets nginx sidecar container which listens on port 80
    resources:
      requests:
        cpu: "20m"
        memory: "50Mi"
      limits:
        memory: "512Mi"
  keda:
    max: "3000"
    min: "1"

application:
  <<: *default-app
  name: auth-token-api
  serviceContext: /main/
  serviceUri: https://dev.auth.bereal.team/main/
  apiPrefix: ''
  security: false
  authentication: false 
  maxConnections: 500
  blacklist:
    userAgents:
      - "NotificationServiceExtension"

auth:
  token:
    persistenceMode: direct
    persistenceBatchSize: 1
    persistenceAutoFlush: false
    persistenceAwait: true
    doubleWrite: 100
  token-api:
    isPasskey2faEnabled: true
  clients: *auth-clients
  signin: *auth-signin
  antibot:
    recaptcha:
      enabled: true
      keys:
        ios: 6LcJ5B8oAAAAAEvKW-kzdvhFw2A8fto3FhekXrdS
    arkose:
      enabled: true

pod:
  namespace: auth
  domain: auth
  feature: token
  name: auth-token-api
  type: api
  registry: us-central1-docker.pkg.dev/shared-build-all/bereal
  command: domains/auth/token-api/main.js
  version: "8af49e1"
  image: bereal-monolith
  env: dev
  envRoot: dev
build-info:
  app: auth-token-api
  version: "8af49e1"
  branch: main
  commit: "8af49e1"
  env: dev
  envRoot: dev
#  buildTime:

ingress:
  enable: true
  suffix: main
